<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EaseAI Widget - 简单演示</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }

      .section {
        margin-bottom: 30px;
      }

      .section h2 {
        color: #666;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }

      .code-block {
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin: 15px 0;
        overflow-x: auto;
      }

      .code-block code {
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }

      .button {
        background: #007cba;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }

      .button:hover {
        background: #005a87;
      }

      .status {
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
      }

      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }

      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>EaseAI Widget 简单演示</h1>

      <div class="section">
        <h2>Widget状态</h2>
        <div id="status" class="status error">Widget未加载</div>
      </div>

      <div class="section">
        <h2>使用方法</h2>
        <p>只需要在页面中添加以下代码：</p>
        <div class="code-block">
          <code>
            &lt;script src="../dist-widget/easeai-widget.js" data-agent-id="your-agent-id"
            data-title="客服助手"&gt; &lt;/script&gt;
          </code>
        </div>
      </div>

      <div class="section">
        <h2>测试功能</h2>
        <button class="button" onclick="testWidget()">测试Widget</button>
        <button class="button" onclick="checkStatus()">检查状态</button>
      </div>

      <div class="section">
        <h2>说明</h2>
        <p>
          这是一个简单的EaseAI
          Widget演示页面。Widget会在页面右下角显示一个聊天图标，点击即可开始对话。
        </p>
      </div>
    </div>

    <!-- 加载 EaseAI Widget -->
    <script
      src="../dist-widget/easeai-widget.js"
      data-agent-id="demo-simple-agent"
      data-title="简单客服"
      data-subtitle="我是您的助手"
      data-welcome-message="您好！有什么可以帮助您的吗？"
    ></script>

    <script>
      // 检查Widget状态
      function checkStatus() {
        const statusEl = document.getElementById('status')
        if (window.EaseAI) {
          statusEl.className = 'status success'
          statusEl.textContent = 'Widget已成功加载'
        } else {
          statusEl.className = 'status error'
          statusEl.textContent = 'Widget未加载'
        }
      }

      // 测试Widget功能
      function testWidget() {
        if (window.EaseAI) {
          alert('Widget已加载！请查看页面右下角的聊天图标。')
        } else {
          alert('Widget未加载，请检查配置。')
        }
      }

      // 页面加载完成后检查状态
      window.addEventListener('load', () => {
        setTimeout(checkStatus, 1000)
      })
    </script>
  </body>
</html>
