# 🤖 EaseAI 客服Widget

> 将AI聊天助手轻松嵌入到任何网站的客服解决方案

## 📖 概述

EaseAI 客服Widget提供了一种简单的方式，让您可以将智能AI客服功能集成到任何网站中。用户只需要添加一行JavaScript代码，就能为网站增加专业的AI客服功能。

## 🎯 核心特性

### ✨ 用户体验
- **🎨 现代化UI设计** - 美观的聊天界面，支持亮/暗主题
- **📱 响应式布局** - 自动适配桌面和移动设备
- **⚡ 实时对话** - 流畅的聊天体验，支持打字指示器
- **🔔 消息提醒** - 未读消息红点提醒功能

### 🛠️ 开发友好
- **🚀 一行代码集成** - 最简单的部署方式
- **⚙️ 灵活配置** - 丰富的自定义选项
- **📡 JavaScript API** - 完整的程序控制接口
- **🎪 事件监听** - 支持消息事件回调

### 🔧 技术优势
- **📦 单文件部署** - 所有依赖打包为一个JS文件
- **🌐 跨域支持** - 可嵌入任何网站
- **💾 轻量级** - 压缩后文件大小控制在合理范围
- **🛡️ 安全隔离** - 独立的样式和脚本环境

## 🚀 快速开始

### 方法一：脚本标签（推荐）

最简单的使用方式，适合大多数场景。**重要：必须提供有效的agentId**：

```html
<script src="https://your-domain.com/easeai-widget.js"
        data-agent-id="your-agent-id"
        data-title="在线客服"
        data-subtitle="我们将为您提供帮助"
        data-api-endpoint="/prod-api">
</script>
```

### 方法二：JavaScript调用

提供更多控制和配置选项：

```javascript
(function(w,d,s,o,f,js,fjs){
  w['easeai']=w['easeai']||function(){
  (w['easeai'].q=w['easeai'].q||[]).push(arguments)};
  js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
  js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
})(window,document,'script','easeai','https://your-domain.com/easeai-widget.js');

easeai('init', {
  agentId: 'your-agent-id',        // 必需：助手ID
  title: '智能客服',
  subtitle: '有什么可以帮助您的？',
  position: 'bottom-right',
  theme: 'light',
  apiEndpoint: '/prod-api'         // API端点配置
});
```

## 🎛️ 配置选项

| 参数               | 类型   | 默认值                         | 说明                                            |
| ------------------ | ------ | ------------------------------ | ----------------------------------------------- |
| `agentId`          | string | -                              | **必需** - AI助手的唯一标识符，用于创建聊天会话 |
| `title`            | string | '在线客服'                     | Widget标题栏显示的标题                          |
| `subtitle`         | string | '我们将为您提供帮助'           | Widget标题栏的副标题                            |
| `avatar`           | string | -                              | 客服头像URL                                     |
| `welcomeMessage`   | string | '您好！有什么可以帮助您的吗？' | 初始欢迎消息                                    |
| `inputPlaceholder` | string | '请输入您的问题...'            | 输入框占位符文本                                |
| `position`         | string | 'bottom-right'                 | Widget位置：`'bottom-right'` \| `'bottom-left'` |
| `theme`            | string | 'light'                        | 主题：`'light'` \| `'dark'`                     |
| `primaryColor`     | string | '#4c5cec'                      | 主色调                                          |
| `apiEndpoint`      | string | '/prod-api'                    | API接口地址，用于后端通信                       |

### 🔑 必需参数详解

#### agentId（必需）
- **作用**：指定要使用的AI助手的唯一标识符
- **重要性**：Widget基于助手ID创建聊天会话，没有有效的agentId将无法正常工作
- **获取方式**：从EaseAI管理后台获取助手的ID
- **示例**：`'agent_12345'`, `'customer-service-bot'`

#### apiEndpoint（可选但推荐）
- **作用**：指定后端API的基础地址
- **默认值**：`'/prod-api'`
- **用途**：用于获取助手token、创建会话、发送消息等API调用
- **示例**：`'/api'`, `'https://api.example.com'`

## 🔌 JavaScript API

Widget提供了完整的JavaScript API用于程序控制：

### 基础控制

```javascript
// 显示Widget
easeai('show');

// 隐藏Widget
easeai('hide');

// 切换显示状态
easeai('toggle');

// 销毁Widget
easeai('destroy');
```

### 消息处理

```javascript
// 发送消息
easeai('sendMessage', '您好，我需要帮助');

// 监听消息事件
easeai('onMessage', function(data) {
  console.log('收到消息:', data);
  if (data.type === 'user') {
    // 用户发送的消息
  } else if (data.type === 'bot') {
    // AI回复的消息
  }
});
```

### 高级API

```javascript
// 直接访问Widget实例
window.EaseAI.show();
window.EaseAI.hide();
window.EaseAI.sendMessage('测试消息');

// 检查Widget状态
if (window.EaseAI.isInitialized) {
  console.log('Widget已初始化');
}
```

## 🎨 自定义样式

Widget设计为样式隔离，但支持一些自定义选项：

```javascript
easeai('init', {
  agentId: 'your-agent-id',
  theme: 'dark',                    // 暗色主题
  primaryColor: '#ff6b6b',         // 自定义主色调
  position: 'bottom-left'          // 左下角位置
});
```

## 📱 响应式设计

Widget自动适配不同设备：

- **桌面端**：380x600px的浮动窗口，位于屏幕右下角
- **移动端**：全屏显示，提供更好的触摸体验
- **平板**：自适应界面，兼顾触摸和显示效果

## 🔧 开发构建

### 开发环境

```bash
# 开发模式（监听文件变化）
pnpm dev:widget

# 构建Widget
pnpm build:widget

# 查看示例
# 在浏览器中打开 widget-examples/demo.html
```

### 文件结构

```
src/
├── views/widget/
│   └── CustomerService.vue    # 客服组件
├── widget.ts                  # Widget入口文件
├── components/common/         # 共用组件
└── utils/                     # 工具函数

widget-examples/
└── demo.html                 # 使用示例

vite.widget.config.ts         # Widget构建配置
```

### 构建产物

构建后会在 `dist-widget/` 目录生成：

- `easeai-widget.js` - 完整的Widget文件（包含所有依赖）
- 大小约：~200KB（压缩后）

## 🚀 部署指南

### 1. 构建Widget

```bash
pnpm build:widget
```

### 2. 部署文件

将 `dist-widget/easeai-widget.js` 上传到您的CDN或服务器。

### 3. 为客户提供集成代码

**简单版本**：
```html
<script src="https://your-domain.com/easeai-widget.js"
        data-agent-id="AGENT_ID_HERE">
</script>
```

**完整版本**：
```html
<script>
(function(w,d,s,o,f,js,fjs){
  w['easeai']=w['easeai']||function(){
  (w['easeai'].q=w['easeai'].q||[]).push(arguments)};
  js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
  js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
})(window,document,'script','easeai','https://your-domain.com/easeai-widget.js');

easeai('init', {
  agentId: 'AGENT_ID_HERE',
  title: '客户服务',
  subtitle: '我们随时为您服务'
});
</script>
```

## 🆚 脚本方式 vs iframe方式对比

| 特性           | 脚本方式 ✅     | iframe方式        |
| -------------- | -------------- | ----------------- |
| **集成复杂度** | 一行代码       | 需要iframe标签    |
| **性能**       | 优秀，按需加载 | 较好              |
| **样式控制**   | 灵活，可定制   | 受限于iframe      |
| **通信能力**   | 直接JavaScript | 需要postMessage   |
| **SEO友好**    | 是             | 否                |
| **移动端体验** | 优秀，全屏适配 | 一般              |
| **安全性**     | 需要信任域名   | 完全隔离          |
| **维护更新**   | 远程自动更新   | 需要更新embed代码 |

## 🔒 安全考虑

1. **域名白名单**：建议在后端验证请求来源域名
2. **API安全**：使用HTTPS和适当的身份验证
3. **内容过滤**：对用户输入进行适当的过滤和验证
4. **XSS防护**：Widget内部已实现基础XSS防护

## 📊 最佳实践

### 性能优化
- 使用CDN部署Widget文件
- 启用gzip压缩
- 考虑使用service worker缓存

### 用户体验
- 设置合适的欢迎消息
- 提供清晰的使用指导
- 在合适的时机显示Widget

### 集成建议
- 在页面加载完成后初始化Widget
- 为重要页面设置特定的agentId
- 监听消息事件进行分析统计

## 🐛 故障排除

### 常见问题

1. **Widget不显示**
   - 检查agentId是否正确
   - 确认脚本文件是否正确加载
   - 查看浏览器控制台错误信息

2. **样式问题**
   - 检查是否有CSS冲突
   - 确认z-index设置
   - 尝试不同的position配置

3. **消息发送失败**
   - 检查API端点配置
   - 确认网络连接
   - 查看后端日志

### 调试技巧

```javascript
// 启用调试模式
localStorage.setItem('easeai-debug', 'true');

// 查看Widget状态
console.log(window.EaseAI);

// 监听所有事件
easeai('onMessage', console.log);
```

## 📞 技术支持

如果您在集成过程中遇到问题，可以：

1. 查看 `widget-examples/demo.html` 示例
2. 检查浏览器开发者工具的控制台
3. 联系技术支持团队

## 🔄 更新日志

### v1.0.0
- ✨ 初始版本发布
- 🎨 现代化UI设计
- 📱 响应式布局支持
- 🔌 完整的JavaScript API
- 📦 单文件部署方案

---

**Happy Coding! 🚀** 