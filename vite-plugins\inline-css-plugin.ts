import fs from 'fs'
import path from 'path'
import type { Plugin } from 'vite'

export function inlineCSSPlugin(): Plugin {
  let cssContent = ''

  return {
    name: 'inline-css-plugin',
    apply: 'build',
    generateBundle(options, bundle) {
      // 查找CSS文件
      const cssFiles = Object.keys(bundle).filter(fileName => fileName.endsWith('.css'))

      if (cssFiles.length > 0) {
        // 获取CSS内容
        cssContent = cssFiles
          .map(fileName => {
            const cssAsset = bundle[fileName]
            if (cssAsset.type === 'asset' && typeof cssAsset.source === 'string') {
              return cssAsset.source
            }
            return ''
          })
          .join('\n')

        // 删除CSS文件，因为我们要内联到JS中
        cssFiles.forEach(fileName => {
          delete bundle[fileName]
        })

        // 查找主JS文件并注入CSS
        const jsFiles = Object.keys(bundle).filter(fileName => fileName.endsWith('.js'))
        jsFiles.forEach(fileName => {
          const jsAsset = bundle[fileName]
          if (jsAsset.type === 'chunk') {
            // 在JS文件开头注入CSS注入代码
            const cssInjectionCode = `
// Auto-injected CSS for Widget Shadow DOM
(function() {
  const CSS_CONTENT = ${JSON.stringify(cssContent)};
  
  // 将CSS注入函数绑定到全局对象
  window.__EASEAI_WIDGET_CSS__ = CSS_CONTENT;
  
  // 如果Shadow DOM已经存在，立即注入CSS
  if (window.__EASEAI_SHADOW_ROOT__) {
    injectCSS(window.__EASEAI_SHADOW_ROOT__, CSS_CONTENT);
  }
  
  function injectCSS(shadowRoot, cssContent) {
    const styleElement = document.createElement('style');
    styleElement.textContent = cssContent;
    shadowRoot.appendChild(styleElement);
  }
})();

`
            jsAsset.code = cssInjectionCode + jsAsset.code
          }
        })
      }
    },
  }
}
