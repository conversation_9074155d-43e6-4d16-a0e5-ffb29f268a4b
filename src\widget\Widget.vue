<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

// Props接口定义
interface WidgetConfig {
  agentId?: string
  title?: string
  subtitle?: string
  avatar?: string
  welcomeMessage?: string
  inputPlaceholder?: string
  position?: 'bottom-right' | 'bottom-left'
  theme?: 'light' | 'dark'
  primaryColor?: string
  apiEndpoint?: string
}

interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: number
}

// Props
const props = defineProps<{
  config: WidgetConfig
}>()

// 响应式数据
const isExpanded = ref(false)
const inputText = ref('')
const isSending = ref(false)
const isTyping = ref(false)
const unreadCount = ref(0)
const messages = ref<ChatMessage[]>([])
const chatContentRef = ref<HTMLElement>()

// resolved 添加助手会话相关状态
const agentInfo = ref<any>(null)
const conversationId = ref<string>('')
const agentUserToken = ref<string>('')
const isInitialized = ref(false)
const initializationError = ref<string>('')

// resolved HTTP请求工具函数，替代API导入
function createHttpClient(baseURL?: string, token?: string) {
  const apiEndpoint = baseURL || props.config.apiEndpoint || '/prod-api'

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  }

  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  return {
    async get<T>(url: string): Promise<{ data: T }> {
      const response = await fetch(`${apiEndpoint}${url}`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return { data }
    },

    async post<T>(url: string, body?: any): Promise<{ data: T }> {
      const response = await fetch(`${apiEndpoint}${url}`, {
        method: 'POST',
        headers,
        body: body ? JSON.stringify(body) : undefined,
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return { data }
    },
  }
}

// 简化的SVG图标组件，使用内联SVG减小体积
const createSvgIcon = (iconName: string) => {
  const icons: Record<string, string> = {
    chat: `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/></svg>`,
    close: `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>`,
    send: `<svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>`,
  }
  return icons[iconName] || icons.chat
}

// 计算属性
const defaultAvatar = computed(() => {
  return (
    agentInfo.value?.emoji ||
    'https://ui-avatars.com/api/?name=AI&background=4c5cec&color=fff&size=40&rounded=true'
  )
})

const userAvatar = computed(() => {
  return 'https://ui-avatars.com/api/?name=User&background=28a745&color=fff&size=32&rounded=true'
})

const isMobile = computed(() => {
  return window.innerWidth <= 768
})

// resolved 获取助手信息和创建会话（展示模式）
async function initializeAgent() {
  if (!props.config.agentId) {
    initializationError.value = '缺少agentId配置'
    return false
  }

  try {
    console.log('展示模式：模拟初始化助手，agentId:', props.config.agentId)

    // resolved 展示模式：使用模拟数据而不是真实API调用
    await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟

    // 模拟助手信息
    agentInfo.value = {
      id: props.config.agentId,
      name: props.config.title || '智能客服助手',
      description: props.config.subtitle || '我是您的专属AI助手，随时为您提供帮助',
      emoji:
        props.config.avatar ||
        'https://ui-avatars.com/api/?name=AI&background=4c5cec&color=fff&size=40&rounded=true',
      status: 1, // 已发布状态
    }

    // 模拟token
    agentUserToken.value = `demo-token-${Date.now()}`

    console.log('展示模式：助手信息初始化成功', agentInfo.value)
    return true
  } catch (error) {
    console.error('展示模式：初始化失败:', error)
    initializationError.value = '展示模式初始化失败'
    return false
  }
}

// resolved 创建聊天会话（展示模式）
async function createConversation() {
  if (!props.config.agentId || !agentUserToken.value) {
    console.error('展示模式：缺少agentId或token')
    return false
  }

  try {
    console.log('展示模式：模拟创建会话，助手ID:', props.config.agentId)

    // resolved 展示模式：模拟会话创建
    await new Promise(resolve => setTimeout(resolve, 300)) // 模拟网络延迟

    // 生成模拟会话ID
    conversationId.value = `demo-conversation-${Date.now()}`

    console.log('展示模式：会话创建成功，ID:', conversationId.value)
    return true
  } catch (error) {
    console.error('展示模式：创建会话失败:', error)
    initializationError.value = '展示模式：创建会话失败'
    return false
  }
}

// 方法
async function toggleWidget() {
  isExpanded.value = !isExpanded.value
  if (isExpanded.value) {
    unreadCount.value = 0

    // resolved 如果还没有初始化，先初始化助手和创建会话
    if (!isInitialized.value && props.config.agentId) {
      const agentInitialized = await initializeAgent()
      if (agentInitialized) {
        const conversationCreated = await createConversation()
        if (conversationCreated) {
          isInitialized.value = true
          // 显示欢迎消息
          setTimeout(() => {
            if (messages.value.length === 0) {
              const welcomeMessage: ChatMessage = {
                id: 'welcome-1',
                text:
                  props.config.welcomeMessage ||
                  agentInfo.value?.description ||
                  '您好！我是EaseAI智能客服，有什么可以帮助您的吗？',
                isUser: false,
                timestamp: Date.now(),
              }
              messages.value.push(welcomeMessage)
            }
          }, 500)
        }
      }
    }

    nextTick(() => {
      scrollToBottom()
    })
  }
}

function sendMessage() {
  if (!inputText.value.trim() || isSending.value) return

  // resolved 检查是否有有效的会话ID
  if (!conversationId.value) {
    console.error('没有有效的会话ID')
    return
  }

  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    text: inputText.value,
    isUser: true,
    timestamp: Date.now(),
  }

  messages.value.push(userMessage)
  const messageText = inputText.value
  inputText.value = ''

  nextTick(() => {
    scrollToBottom()
  })

  // resolved 发送消息到服务器
  sendToServer(messageText)
}

async function sendToServer(message: string) {
  if (!conversationId.value || !agentUserToken.value) {
    console.error('展示模式：缺少会话ID或token')
    return
  }

  isSending.value = true
  isTyping.value = true

  try {
    console.log('展示模式：模拟发送消息:', message)

    // resolved 展示模式：模拟AI响应
    await new Promise(resolve => setTimeout(resolve, 1500)) // 模拟响应延迟

    // 模拟不同类型的AI响应
    const responses = [
      `感谢您的问题："${message}"。作为AI助手，我很乐意为您提供帮助。您可以问我任何问题！`,
      `我理解您想了解关于"${message}"的信息。这是一个很好的问题，让我为您详细解答...`,
      `关于您提到的"${message}"，我建议您可以从以下几个方面来考虑：\n1. 首先分析问题的核心\n2. 寻找相关的解决方案\n3. 制定具体的行动计划`,
      `很高兴您问到"${message}"这个话题。根据我的理解，这通常涉及到多个方面的考量。我可以为您提供一些专业的建议和指导。`,
      `我注意到您关心"${message}"这个问题。这确实是一个值得深入讨论的话题。让我为您分析一下相关的要点...`,
    ]

    // 随机选择一个响应
    const randomResponse = responses[Math.floor(Math.random() * responses.length)]

    const botMessage: ChatMessage = {
      id: `demo-${Date.now()}`,
      text: randomResponse,
      isUser: false,
      timestamp: Date.now(),
    }

    messages.value.push(botMessage)

    nextTick(() => {
      scrollToBottom()
    })

    // 如果widget是收起状态，增加未读计数
    if (!isExpanded.value) {
      unreadCount.value++
    }

    console.log('展示模式：AI响应发送成功')
  } catch (error) {
    console.error('展示模式：发送消息失败:', error)

    // 显示错误消息
    const errorMessage: ChatMessage = {
      id: `error-${Date.now()}`,
      text: '抱歉，展示模式下出现了问题，请稍后重试。',
      isUser: false,
      timestamp: Date.now(),
    }
    messages.value.push(errorMessage)
  } finally {
    isSending.value = false
    isTyping.value = false
  }
}

function scrollToBottom() {
  if (chatContentRef.value) {
    chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight
  }
}

function formatMessage(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/\n/g, '<br>')
}

function formatTime(timestamp: number): string {
  // 简化的时间格式化
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  return date.toLocaleDateString()
}

function onInputKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 生命周期
onMounted(() => {
  console.log('Customer service widget mounted with config:', props.config)
})

onUnmounted(() => {
  // 清理工作
})

// 监听配置变化
watch(
  () => props.config,
  newConfig => {
    console.log('Widget config updated:', newConfig)
    // resolved 如果agentId发生变化，重置状态
    if (newConfig.agentId !== props.config.agentId) {
      isInitialized.value = false
      conversationId.value = ''
      agentUserToken.value = ''
      agentInfo.value = null
      messages.value = []
    }
  },
  { deep: true },
)
</script>

<template>
  <div
    class="customer-service-widget"
    :class="{
      'widget-expanded': isExpanded,
      'widget-collapsed': !isExpanded,
      'widget-mobile': isMobile,
    }"
  >
    <!-- 客服触发按钮 -->
    <div v-if="!isExpanded" class="widget-trigger" @click="toggleWidget">
      <div class="trigger-icon" v-html="createSvgIcon('chat')"></div>
      <div v-if="unreadCount > 0" class="unread-badge">{{ unreadCount }}</div>
    </div>

    <!-- 客服聊天界面 -->
    <div v-if="isExpanded" class="widget-chat-container">
      <!-- 标题栏 -->
      <div class="widget-header">
        <div class="header-info">
          <img :src="config.avatar || defaultAvatar" alt="客服头像" class="avatar" />
          <div class="info">
            <div class="title">{{ config.title || agentInfo?.name || '在线客服' }}</div>
            <div class="subtitle">
              {{ config.subtitle || agentInfo?.description || '我们将为您提供帮助' }}
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button class="action-btn" @click="toggleWidget">
            <span v-html="createSvgIcon('close')"></span>
          </button>
        </div>
      </div>

      <!-- 聊天内容 -->
      <div ref="chatContentRef" class="widget-chat-content">
        <div class="messages-container">
          <!-- 初始化错误提示 -->
          <div v-if="initializationError" class="error-message">
            <div class="error-text">{{ initializationError }}</div>
          </div>

          <!-- 欢迎消息 -->
          <div v-else-if="messages.length === 0 && !isTyping" class="welcome-message">
            <div class="welcome-text">
              {{
                config.welcomeMessage || agentInfo?.description || '您好！有什么可以帮助您的吗？'
              }}
            </div>
          </div>

          <!-- 聊天消息 -->
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message-item"
            :class="{ 'message-user': message.isUser, 'message-bot': !message.isUser }"
          >
            <div class="message-avatar">
              <img
                :src="message.isUser ? userAvatar : config.avatar || defaultAvatar"
                :alt="message.isUser ? '用户' : '客服'"
              />
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.text)"></div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <!-- 正在输入指示器 -->
          <div v-if="isTyping" class="typing-indicator">
            <div class="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="widget-input-area">
        <div class="input-container">
          <input
            v-model="inputText"
            type="text"
            :placeholder="config.inputPlaceholder || '请输入您的问题...'"
            class="message-input"
            :disabled="!isInitialized || !!initializationError"
            @keydown="onInputKeydown"
          />
          <button
            class="send-button"
            :disabled="!inputText.trim() || isSending || !isInitialized || !!initializationError"
            @click="sendMessage"
          >
            <span v-html="createSvgIcon('send')"></span>
          </button>
        </div>
        <!-- resolved 添加免责声明 -->
        <div class="disclaimer">内容由 AI 生成，仅供参考</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.customer-service-widget {
  position: fixed;
  z-index: 999999;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  &.widget-collapsed {
    bottom: 20px;
    right: 20px;
  }

  &.widget-expanded {
    bottom: 20px;
    right: 20px;
    width: 380px;
    height: 600px;

    @media (max-width: 768px) {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100vw;
      height: 100vh;
    }
  }
}

.widget-trigger {
  width: 60px;
  height: 60px;
  background: #4c5cec;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  .trigger-icon {
    color: white;
    font-size: 24px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    /* resolved 确保SVG图标正确显示 */
    svg {
      width: 100%;
      height: 100%;
      display: block;
    }
  }

  .unread-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}

.widget-chat-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.widget-header {
  background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(76, 92, 236, 0.3);

  .header-info {
    display: flex;
    align-items: center;
    gap: 12px;

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .info {
      .title {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 2px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .subtitle {
        font-size: 12px;
        opacity: 0.9;
        font-weight: 400;
      }
    }
  }

  .header-actions {
    .action-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      cursor: pointer;
      padding: 8px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }

      /* resolved 确保SVG图标正确显示 */
      svg {
        width: 20px;
        height: 20px;
        display: block;
      }
    }
  }
}

.widget-chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(76, 92, 236, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(76, 92, 236, 0.5);
  }
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  text-align: center;
  padding: 20px;
  color: #666;

  .welcome-text {
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/* 错误消息样式 */
.error-message {
  text-align: center;
  padding: 20px;
  color: #ff4757;

  .error-text {
    background: #fff5f5;
    padding: 12px 16px;
    border-radius: 8px;
    display: inline-block;
    border: 1px solid #fed7d7;
    color: #c53030;
  }
}

.message-item {
  display: flex;
  gap: 8px;
  animation: fadeInUp 0.3s ease-out;

  &.message-user {
    flex-direction: row-reverse;

    .message-content {
      background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
      color: white;
      margin-left: 40px;
      border-bottom-right-radius: 4px;

      .message-time {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  &.message-bot {
    .message-content {
      background: white;
      margin-right: 40px;
      border: 1px solid #e9ecef;
      border-bottom-left-radius: 4px;
    }
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid rgba(76, 92, 236, 0.1);
    }
  }

  .message-content {
    max-width: 70%;
    padding: 10px 14px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
    }

    .message-text {
      font-size: 14px;
      line-height: 1.5;
      word-wrap: break-word;
      margin-bottom: 4px;
    }

    .message-time {
      font-size: 11px;
      opacity: 0.7;
      font-weight: 400;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;

  .typing-dots {
    display: flex;
    gap: 3px;
    padding: 8px 12px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);

    span {
      width: 6px;
      height: 6px;
      background: #ccc;
      border-radius: 50%;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.widget-input-area {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 16px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);

  .input-container {
    display: flex;
    gap: 10px;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 4px;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &:focus-within {
      border-color: #4c5cec;
      background: white;
      box-shadow: 0 0 0 3px rgba(76, 92, 236, 0.1);
    }

    .message-input {
      flex: 1;
      border: none;
      background: transparent;
      padding: 10px 16px;
      font-size: 14px;
      outline: none;
      color: #333;

      &::placeholder {
        color: #999;
        font-weight: 400;
      }
    }

    .send-button {
      width: 38px;
      height: 38px;
      background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
      border: none;
      border-radius: 50%;
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(76, 92, 236, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #3a4bc8 0%, #4c63d2 100%);
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(76, 92, 236, 0.4);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }

      &:active:not(:disabled) {
        transform: scale(0.95);
      }

      /* resolved 确保SVG图标正确显示 */
      svg {
        width: 18px;
        height: 18px;
        display: block;
      }
    }
  }

  /* resolved 免责声明样式 */
  .disclaimer {
    text-align: center;
    font-size: 11px;
    color: #999;
    margin-top: 8px;
    padding: 0 4px;
    opacity: 0.8;
    line-height: 1.2;
  }
}

/* 移动端适配 */
.widget-mobile {
  .widget-chat-container {
    border-radius: 0;
  }

  .message-item {
    .message-content {
      max-width: 85%;
    }
  }
}
</style>
