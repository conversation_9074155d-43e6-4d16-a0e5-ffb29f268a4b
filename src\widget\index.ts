/**
 * Widget模块索引文件
 * 统一导出Widget相关的组件和工具
 */

// 导出Widget组件
export { default as Widget } from './Widget.vue'
// 保持向后兼容
export { default as CustomerService } from './Widget.vue'

// 导出样式加载器
export * from './style-loader'

// 导出类型定义
export interface WidgetConfig {
  agentId?: string
  title?: string
  subtitle?: string
  avatar?: string
  welcomeMessage?: string
  inputPlaceholder?: string
  position?: 'bottom-right' | 'bottom-left'
  theme?: 'light' | 'dark'
  primaryColor?: string
  apiEndpoint?: string
}

export interface ChatMessage {
  id: string
  text: string
  isUser: boolean
  timestamp: number
}
