<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EaseAI Widget - 基础演示</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .demo-header {
        background: linear-gradient(135deg, #4c5cec 0%, #5a67d8 100%);
        color: white;
        padding: 40px;
        text-align: center;
      }

      .demo-header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
        font-weight: 700;
      }

      .demo-header p {
        font-size: 1.1rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
      }

      .demo-content {
        padding: 40px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h2 {
        font-size: 1.5rem;
        margin-bottom: 20px;
        color: #2d3748;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .demo-section h2::before {
        content: '🚀';
        font-size: 1.2rem;
      }

      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .feature-card {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        border-left: 4px solid #4c5cec;
      }

      .feature-card h3 {
        color: #4c5cec;
        margin-bottom: 10px;
        font-size: 1.1rem;
      }

      .feature-card p {
        color: #666;
        font-size: 0.9rem;
      }

      .code-example {
        background: #1a202c;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 8px;
        overflow-x: auto;
        margin: 20px 0;
        position: relative;
      }

      .code-example::before {
        content: 'HTML';
        position: absolute;
        top: 10px;
        right: 15px;
        background: #4c5cec;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
      }

      .code-example pre {
        margin: 0;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 0.9rem;
        line-height: 1.5;
      }

      .status-indicator {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
      }

      .status-indicator.active {
        background: #d4edda;
        color: #155724;
      }

      .status-indicator.inactive {
        background: #f8d7da;
        color: #721c24;
      }

      .status-indicator::before {
        content: '●';
        font-size: 0.8rem;
      }

      .demo-actions {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        margin-top: 20px;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .btn-primary {
        background: #4c5cec;
        color: white;
      }

      .btn-primary:hover {
        background: #3a4bc8;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 92, 236, 0.3);
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background: #545b62;
        transform: translateY(-2px);
      }

      .btn-outline {
        background: transparent;
        color: #4c5cec;
        border: 2px solid #4c5cec;
      }

      .btn-outline:hover {
        background: #4c5cec;
        color: white;
      }

      .demo-footer {
        background: #f8f9fa;
        padding: 30px 40px;
        text-align: center;
        border-top: 1px solid #e9ecef;
      }

      .demo-footer p {
        color: #666;
        margin-bottom: 15px;
      }

      .demo-footer .links {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
      }

      .demo-footer .links a {
        color: #4c5cec;
        text-decoration: none;
        font-weight: 500;
      }

      .demo-footer .links a:hover {
        text-decoration: underline;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        .demo-header {
          padding: 30px 20px;
        }

        .demo-header h1 {
          font-size: 2rem;
        }

        .demo-content {
          padding: 30px 20px;
        }

        .feature-grid {
          grid-template-columns: 1fr;
        }

        .demo-actions {
          flex-direction: column;
        }

        .btn {
          justify-content: center;
        }
      }

      /* Widget样式隔离测试 */
      .style-pollution-test {
        margin: 20px 0;
        padding: 20px;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
      }

      .style-pollution-test h3 {
        color: #856404;
        margin-bottom: 10px;
      }

      .style-pollution-test p {
        color: #664d03;
        font-size: 0.9rem;
      }

      /* 故意添加一些可能冲突的样式 */
      .style-pollution-test * {
        border: 1px dashed #ffc107 !important;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <!-- 页面头部 -->
      <div class="demo-header">
        <h1>EaseAI 智能客服Widget</h1>
        <p>基础演示 - 展示Widget的核心功能和集成方式</p>
      </div>

      <!-- 主要内容 -->
      <div class="demo-content">
        <!-- 功能特性 -->
        <div class="demo-section">
          <h2>主要特性</h2>
          <div class="feature-grid">
            <div class="feature-card">
              <h3>🤖 AI助手对话</h3>
              <p>基于助手ID创建专属聊天会话，提供智能问答服务</p>
            </div>
            <div class="feature-card">
              <h3>🎨 可定制界面</h3>
              <p>支持自定义标题、副标题、头像、颜色等界面元素</p>
            </div>
            <div class="feature-card">
              <h3>📱 响应式设计</h3>
              <p>完美适配桌面端和移动端，提供一致的用户体验</p>
            </div>
            <div class="feature-card">
              <h3>🔌 便捷集成</h3>
              <p>支持Script标签和JavaScript API两种集成方式</p>
            </div>
          </div>
        </div>

        <!-- 当前状态 -->
        <div class="demo-section">
          <h2>Widget状态</h2>
          <div id="widgetStatus" class="status-indicator inactive">
            <span>Widget未激活</span>
          </div>
          <p style="margin-top: 10px; color: #666">
            Widget当前运行在展示模式下，使用模拟数据进行演示
          </p>
        </div>

        <!-- 集成代码示例 -->
        <div class="demo-section">
          <h2>集成代码</h2>
          <p>将以下代码添加到您的网页中即可使用Widget：</p>

          <div class="code-example">
            <pre>
&lt;!-- 方式一：Script标签集成 --&gt;
&lt;script src="../dist-widget/easeai-widget.js" 
        data-easeai-agent-id="demo-agent-123"
        data-easeai-title="智能客服"
        data-easeai-subtitle="我们随时为您提供帮助"
        data-easeai-welcome-message="您好！有什么可以帮助您的吗？"&gt;
&lt;/script&gt;</pre
            >
          </div>

          <div class="code-example">
            <pre>
&lt;!-- 方式二：JavaScript API集成 --&gt;
&lt;script&gt;
(function(w,d,s,o,f,js,fjs){
  w['easeai']=w['easeai']||function(){
  (w['easeai'].q=w['easeai'].q||[]).push(arguments)};
  js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
  js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
})(window,document,'script','easeai','../dist-widget/easeai-widget.js');

easeai('init', {
  agentId: 'demo-agent-123',
  title: '智能客服',
  subtitle: '我们随时为您提供帮助',
  welcomeMessage: '您好！有什么可以帮助您的吗？'
});
&lt;/script&gt;</pre
            >
          </div>
        </div>

        <!-- 样式隔离测试 -->
        <div class="demo-section">
          <div class="style-pollution-test">
            <h3>⚠️ 样式隔离测试</h3>
            <p>
              此区域包含可能与Widget冲突的CSS样式，用于测试Widget的样式隔离能力。Widget应该不受这些样式影响。
            </p>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="demo-section">
          <h2>演示操作</h2>
          <div class="demo-actions">
            <button class="btn btn-primary" onclick="testWidget()">🚀 测试Widget功能</button>
            <button class="btn btn-secondary" onclick="toggleWidget()">👁️ 切换Widget显示</button>
            <a href="integration-test.html" class="btn btn-outline"> 🔗 查看集成测试 </a>
            <a href="stress-test.html" class="btn btn-outline"> ⚡ 压力测试 </a>
          </div>
        </div>
      </div>

      <!-- 页面底部 -->
      <div class="demo-footer">
        <p>EaseAI 智能客服Widget - 让AI助手轻松集成到您的网站</p>
        <div class="links">
          <a href="#docs">📚 使用文档</a>
          <a href="#api">🔧 API参考</a>
          <a href="#examples">💡 更多示例</a>
          <a href="#support">🤝 技术支持</a>
        </div>
      </div>
    </div>

    <!-- Widget集成 -->
    <script
      src="../dist-widget/easeai-widget.js"
      data-easeai-agent-id="demo-basic-agent"
      data-easeai-title="EaseAI 客服"
      data-easeai-subtitle="基础演示助手"
      data-easeai-welcome-message="欢迎使用EaseAI Widget基础演示！"
      data-easeai-input-placeholder="请输入您的问题..."
      data-easeai-primary-color="#4c5cec"
    ></script>

    <script>
      // 页面交互功能
      function updateWidgetStatus() {
        const statusEl = document.getElementById('widgetStatus')
        if (window.EaseAIWidget) {
          statusEl.className = 'status-indicator active'
          statusEl.innerHTML = '<span>Widget已激活</span>'
        } else {
          statusEl.className = 'status-indicator inactive'
          statusEl.innerHTML = '<span>Widget未激活</span>'
        }
      }

      function testWidget() {
        if (window.EaseAIWidget) {
          alert(
            'Widget测试功能：\n✅ Widget已成功加载\n✅ 展示模式运行正常\n✅ 样式隔离有效\n\n请点击右下角的客服图标开始体验！',
          )
        } else {
          alert('❌ Widget未能正确加载，请检查script标签配置')
        }
      }

      function toggleWidget() {
        if (window.EaseAIWidget) {
          // 这里可以添加显示/隐藏Widget的逻辑
          alert('💡 提示：Widget的显示/隐藏功能可以通过API控制')
        } else {
          alert('❌ Widget未加载')
        }
      }

      // 页面加载完成后检查Widget状态
      window.addEventListener('load', () => {
        setTimeout(updateWidgetStatus, 1000)

        // 定期检查Widget状态
        setInterval(updateWidgetStatus, 5000)
      })

      // 添加一些测试用的控制台输出
      console.log('🚀 EaseAI Widget 基础演示页面已加载')
      console.log('📋 可用的测试功能：')
      console.log('  - testWidget(): 测试Widget功能')
      console.log('  - toggleWidget(): 切换Widget显示')
      console.log('  - updateWidgetStatus(): 更新Widget状态')
    </script>
  </body>
</html>
